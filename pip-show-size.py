import os
import subprocess
import importlib.metadata
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_directory_size_fast(path):
    """
    快速计算目录大小（字节）。
    """
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, IOError):
                    # 跳过无法访问的文件
                    continue
    except (OSError, IOError):
        return 0
    return total_size

def get_package_size(package_info):
    """
    获取指定包的磁盘占用大小（MB）。
    """
    package_name, package_version = package_info
    try:
        # 获取包的安装路径
        distribution = importlib.metadata.distribution(package_name)

        # 尝试获取包的文件列表
        if hasattr(distribution, 'files') and distribution.files:
            total_size = 0
            for file in distribution.files:
                try:
                    file_path = distribution.locate_file(file)
                    if file_path and os.path.exists(file_path):
                        if os.path.isfile(file_path):
                            total_size += os.path.getsize(file_path)
                        elif os.path.isdir(file_path):
                            total_size += get_directory_size_fast(file_path)
                except (OSError, IOError):
                    continue
            size_in_mb = total_size / (1024 * 1024)  # 转换为 MB
            return package_name, size_in_mb
        else:
            # 回退到使用 locate_file 方法
            location = distribution.locate_file('')
            if location and os.path.exists(location):
                if os.path.isdir(location):
                    size_in_bytes = get_directory_size_fast(location)
                else:
                    size_in_bytes = os.path.getsize(location)
                size_in_mb = size_in_bytes / (1024 * 1024)  # 转换为 MB
                return package_name, size_in_mb
    except Exception as e:
        logger.debug(f"Error getting size for {package_name}: {e}")
    return package_name, 0

def main():
    start_time = time.time()

    # 获取当前环境中已安装的包
    logger.info("正在获取已安装的包列表...")
    installed_packages = [(pkg.name, pkg.version) for pkg in importlib.metadata.distributions()]
    total_packages = len(installed_packages)
    logger.info(f"找到 {total_packages} 个已安装的包")

    package_sizes = {}
    processed_count = 0

    # 使用线程池并行处理包大小计算
    logger.info("开始计算包大小...")
    max_workers = min(8, os.cpu_count() or 1)  # 限制线程数量避免过度并发

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_package = {
            executor.submit(get_package_size, pkg_info): pkg_info[0]
            for pkg_info in installed_packages
        }

        # 处理完成的任务
        for future in as_completed(future_to_package):
            package_name = future_to_package[future]
            try:
                pkg_name, size = future.result()
                if size > 0:
                    package_sizes[pkg_name] = size

                processed_count += 1
                if processed_count % 10 == 0 or processed_count == total_packages:
                    progress = (processed_count / total_packages) * 100
                    logger.info(f"进度: {processed_count}/{total_packages} ({progress:.1f}%)")

            except Exception as e:
                logger.error(f"处理包 {package_name} 时出错: {e}")
                processed_count += 1

    # 按大小降序排序
    logger.info("正在排序结果...")
    sorted_packages = sorted(package_sizes.items(), key=lambda x: x[1], reverse=True)

    # 计算总大小
    total_size = sum(package_sizes.values())

    # 打印结果
    elapsed_time = time.time() - start_time
    logger.info(f"分析完成，耗时 {elapsed_time:.2f} 秒")

    print(f"\n{'Package':<30} {'Size (MB)':>10}")
    print("-" * 45)
    for package, size in sorted_packages:
        print(f"{package:<30} {size:>10.2f} MB")

    print("-" * 45)
    print(f"{'总计':<30} {total_size:>10.2f} MB")
    print(f"分析了 {len(package_sizes)} 个有效包（共 {total_packages} 个包）")

if __name__ == '__main__':
    main()