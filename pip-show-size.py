import os
import subprocess
import importlib.metadata
from collections import defaultdict

def get_package_size(package_name):
    """
    获取指定包的磁盘占用大小（MB）。
    """
    try:
        # 获取包的安装路径
        distribution = importlib.metadata.distribution(package_name)
        location = distribution.locate_file('')
        if location:
            # 计算包的大小（MB）
            size_in_bytes = subprocess.check_output(['du', '-s', location]).split()[0].decode('utf-8')
            size_in_mb = int(size_in_bytes) / 1024  # 转换为 MB
            return size_in_mb
    except Exception as e:
        print(f"Error getting size for {package_name}: {e}")
    return 0

def main():
    # 获取当前环境中已安装的包
    installed_packages = {pkg.name: pkg.version for pkg in importlib.metadata.distributions()}
    package_sizes = defaultdict(float)

    # 计算每个包的大小
    for package_name in installed_packages:
        size = get_package_size(package_name)
        if size > 0:
            package_sizes[package_name] = size

    # 按大小降序排序
    sorted_packages = sorted(package_sizes.items(), key=lambda x: x[1], reverse=True)

    # 打印结果
    print(f"{'Package':<30} {'Size (MB)':>10}")
    print("-" * 45)
    for package, size in sorted_packages:
        print(f"{package:<30} {size:>10.2f} MB")

if __name__ == '__main__':
    main()